# Makefile para gerenciar o Odoo 18
# Uso: make <comando>

.PHONY: help start stop restart status setup clean logs browser manager install test

# Comando padrão
help:
	@echo "🚀 Comandos disponíveis para o Odoo 18:"
	@echo ""
	@echo "  make start      - Iniciar o Odoo"
	@echo "  make stop       - Parar o Odoo"
	@echo "  make restart    - Reiniciar o Odoo"
	@echo "  make status     - Verificar status do sistema"
	@echo "  make setup      - Configurar banco de dados"
	@echo "  make install    - Instalar dependências"
	@echo "  make clean      - Limpar cache e logs"
	@echo "  make logs       - Ver logs do Odoo"
	@echo "  make browser    - Abrir Odoo no navegador"
	@echo "  make manager    - Abrir gerenciador interativo"
	@echo "  make test       - Executar testes"
	@echo ""
	@echo "📚 Para mais informações, consulte COMO_RODAR.md"

# Iniciar Odoo
start:
	@echo "🚀 Iniciando Odoo..."
	@./rodar_odoo.sh

# Parar Odoo
stop:
	@echo "🛑 Parando Odoo..."
	@./parar_odoo.sh

# Reiniciar Odoo
restart: stop
	@echo "🔄 Aguardando 3 segundos..."
	@sleep 3
	@$(MAKE) start

# Verificar status
status:
	@echo "📊 Verificando status do sistema..."
	@./verificar_sistema.sh

# Configurar banco de dados
setup:
	@echo "🗄️ Configurando banco de dados..."
	@./setup_database.sh

# Instalar dependências
install:
	@echo "📦 Instalando dependências..."
	@if [ ! -d "venv" ]; then python3 -m venv venv; fi
	@source venv/bin/activate && pip install --upgrade pip
	@source venv/bin/activate && pip install -r requirements.txt
	@echo "✅ Dependências instaladas!"

# Limpar cache e logs
clean:
	@echo "🧹 Limpando cache e logs..."
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyc" -delete 2>/dev/null || true
	@find . -name "*.tmp" -delete 2>/dev/null || true
	@find . -name ".DS_Store" -delete 2>/dev/null || true
	@if [ -f "odoo.log" ]; then > odoo.log; fi
	@echo "✅ Limpeza concluída!"

# Ver logs
logs:
	@echo "📝 Últimas 50 linhas do log:"
	@if [ -f "odoo.log" ]; then tail -50 odoo.log; else echo "❌ Arquivo de log não encontrado"; fi

# Abrir no navegador
browser:
	@echo "🌐 Abrindo Odoo no navegador..."
	@if pgrep -f "odoo-bin" > /dev/null; then \
		if command -v open >/dev/null 2>&1; then \
			open http://localhost:8069; \
		elif command -v xdg-open >/dev/null 2>&1; then \
			xdg-open http://localhost:8069; \
		else \
			echo "📱 Abra http://localhost:8069 no seu navegador"; \
		fi; \
	else \
		echo "⚠️ Odoo não está rodando. Execute 'make start' primeiro."; \
	fi

# Gerenciador interativo
manager:
	@./odoo_manager.sh

# Executar testes
test:
	@echo "🧪 Executando testes..."
	@if [ ! -f "odoo.conf" ]; then echo "❌ Configure o sistema primeiro com 'make setup'"; exit 1; fi
	@source venv/bin/activate && python3 odoo-bin --test-enable --stop-after-init -d test_db
	@echo "✅ Testes concluídos!"

# Criar nova base de dados
create-db:
	@echo "🗄️ Criando nova base de dados..."
	@read -p "Nome da base de dados: " dbname; \
	source venv/bin/activate && python3 odoo-bin -d $$dbname -i base --stop-after-init
	@echo "✅ Base de dados criada!"

# Atualizar módulo
update-module:
	@echo "🔄 Atualizando módulo..."
	@read -p "Nome da base de dados: " dbname; \
	read -p "Nome do módulo: " module; \
	source venv/bin/activate && python3 odoo-bin -d $$dbname -u $$module --stop-after-init
	@echo "✅ Módulo atualizado!"

# Instalar módulo
install-module:
	@echo "📦 Instalando módulo..."
	@read -p "Nome da base de dados: " dbname; \
	read -p "Nome do módulo: " module; \
	source venv/bin/activate && python3 odoo-bin -d $$dbname -i $$module --stop-after-init
	@echo "✅ Módulo instalado!"

# Backup da base de dados
backup-db:
	@echo "💾 Fazendo backup da base de dados..."
	@read -p "Nome da base de dados: " dbname; \
	pg_dump $$dbname > backup_$$dbname_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Backup criado!"

# Informações do sistema
info:
	@echo "ℹ️ Informações do Sistema Odoo:"
	@echo "📁 Diretório: $(PWD)"
	@echo "🐍 Python: $(shell python3 --version 2>/dev/null || echo 'Não encontrado')"
	@echo "🗄️ PostgreSQL: $(shell psql --version 2>/dev/null || echo 'Não encontrado')"
	@echo "📦 Módulos: $(shell ls -1 addons 2>/dev/null | wc -l | tr -d ' ') addons disponíveis"
	@echo "🔧 Status: $(shell pgrep -f 'odoo-bin' >/dev/null && echo 'Rodando' || echo 'Parado')"
