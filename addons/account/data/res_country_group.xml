<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data noupdate="1">
        <record id="europe_vat" model="res.country.group">
            <field name="name">European Union VAT</field>
            <field name="code">EU-VAT</field>
            <field name="country_ids" eval="[Command.set([
                ref('base.at'),ref('base.be'),ref('base.bg'),ref('base.hr'),ref('base.cy'),
                ref('base.cz'),ref('base.dk'),ref('base.ee'),ref('base.fi'),ref('base.fr'),
                ref('base.de'),ref('base.gr'),ref('base.hu'),ref('base.ie'),ref('base.it'),
                ref('base.lv'),ref('base.lt'),ref('base.lu'),ref('base.mt'),ref('base.nl'),
                ref('base.pl'),ref('base.pt'),ref('base.ro'),ref('base.sk'),ref('base.si'),
                ref('base.es'),ref('base.se')])]"/>
            <field name="exclude_state_ids" eval="[Command.set([
                ref('base.state_es_ce'), ref('base.state_es_ml'),
                ref('base.state_es_tf'), ref('base.state_es_gc'),
                ref('base.state_nl_bq1'), ref('base.state_nl_bq2'),
                ref('base.state_nl_bq3'),
            ])]"/>
        </record>
    </data>
</odoo>
