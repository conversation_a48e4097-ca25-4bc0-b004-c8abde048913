<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="account.ReceiptSelector">
        <div>
            <t t-if="props.readonly || (!show_sale_receipts.value and ['out_invoice', 'out_receipt'].includes(value))">
                <span t-esc="string" t-att-raw-value="value" />
            </t>
            <t t-else="">
                <t t-call="web.RadioField"/>
            </t>
        </div>
    </t>

</templates>
