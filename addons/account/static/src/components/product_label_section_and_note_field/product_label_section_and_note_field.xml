<?xml version="1.0" encoding="UTF-8" ?>
<templates>

    <t t-name="account.ProductLabelSectionAndNoteField">
        <div class="o_field_product_label_section_and_note_cell">
            <t t-if="isNote()">
                <textarea
                    class="o_input d-print-none border-0 fst-italic"
                    placeholder="Enter a description"
                    rows="1"
                    t-att-class="sectionAndNoteClasses"
                    t-att-readonly="sectionAndNoteIsReadonly"
                    t-att-value="label"
                    t-on-change="(ev) => this.updateLabel(ev.target.value)"
                    t-ref="labelNodeRef"
                />
            </t>
            <t t-elif="isSection()">
                <input
                    type="text"
                    class="o_input text-wrap border-0 fst-italic fw-bold"
                    placeholder="Enter a description"
                    t-att-class="sectionAndNoteClasses"
                    t-att-readonly="sectionAndNoteIsReadonly"
                    t-att-value="label"
                    t-on-change="(ev) => this.updateLabel(ev.target.value)"
                    t-ref="labelNodeRef"
                />
            </t>
            <t t-else="">
                <div class="d-flex align-items-center gap-1">
                    <Many2One t-props="this.m2oProps" cssClass="'w-100'" t-on-keydown="onM2oInputKeydown"/>
                    <t t-if="showLabelVisibilityToggler">
                        <button
                            class="btn fa fa-bars text-start o_external_button px-1"
                            type="button"
                            id="labelVisibilityButtonId"
                            data-tooltip="Click or press enter to add a description"
                            t-on-click="() => this.switchLabelVisibility()"
                        />
                    </t>
                </div>
                <textarea
                    class="o_input d-print-none border-0 fst-italic"
                    placeholder="Enter a description"
                    rows="1"
                    type="text"
                    t-att-class="{ ...sectionAndNoteClasses, 'd-none': !(columnIsProductAndLabel.value and (label or !props.readonly and labelVisibility.value)) }"
                    t-att-readonly="props.readonly and isProductClickable ? '1' : ''"
                    t-att-value="label"
                    t-on-change="(ev) => this.updateLabel(ev.target.value)"
                    t-ref="labelNodeRef"
                />
            </t>
            <t t-if="isPrintMode.value">
                <div class="d-none d-print-block text-wrap" t-out="label"/>
            </t>
        </div>
    </t>

</templates>
