<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_account_reconcile_model_tree" model="ir.ui.view">
            <field name="name">account.reconcile.model.list</field>
            <field name="model">account.reconcile.model</field>
            <field name="arch" type="xml">
                <list string="Bank Reconciliation Move Presets">
                    <field name="sequence" widget="handle" />
                    <field name="name"/>
                    <field name="trigger"/>
                    <field name="match_journal_ids" optional="hidden" widget="many2many_tags"/>
                </list>
            </field>
        </record>

        <record id="view_account_reconcile_model_form" model="ir.ui.view">
            <field name="name">account.reconcile.model.form</field>
            <field name="model">account.reconcile.model</field>
            <field name="arch" type="xml">
                <form string="Operation Templates">
                    <header>
                        <button name="action_set_manual"
                                type="object"
                                string="Set Manual"
                                invisible="trigger == 'manual'"
                                class="oe_highlight"
                                data-hotkey="y"/>
                        <button name="action_set_auto_reconcile"
                                string="Automate"
                                class="oe_highlight"
                                type="object"
                                data-hotkey="q"
                                invisible="trigger == 'auto_reconcile'"/>
                        <field name="trigger" widget="statusbar"/>
                    </header>
                    <field name="active" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button type="object" name="action_reconcile_stat" string="Journal Entries"
                              class="oe_stat_button" icon="fa-book">
                            </button>
                        </div>
                        <div class="oe_title">
                            <label for="name" placeholder="Model Name"/>
                            <h1><field name="name" placeholder="e.g. Bank Fees"/></h1>
                        </div>
                        <group>
                            <group id="filters_left_column">
                                <field name="match_journal_ids"
                                       placeholder="All bank &amp; cash journals"
                                       widget="many2many_tags"
                                       options="{'no_create': True}"/>
                                <field name="match_partner_ids"
                                       widget="many2many_tags"
                                       options="{'no_quick_create': True}"
                                       placeholder="All partners"/>
                                <label for="match_amount"/>
                                <div class="d-flex gap-2">
                                    <field name="match_amount" placeholder="Any amount"/>
                                    <field name="match_amount_min"
                                           invisible="match_amount in (False, 'lower')"
                                           required="match_amount"/>
                                    <span class="o_form_label"
                                          invisible="match_amount != 'between'">and</span>
                                    <field name="match_amount_max"
                                           invisible="match_amount in (False, 'greater')"
                                           required="match_amount == 'between'"/>
                                </div>
                                <label for="match_label"/>
                                <div class="d-flex gap-3">
                                    <field name="match_label" placeholder="Any label"/>
                                    <field name="match_label_param"
                                           invisible="not match_label"
                                           required="match_label"
                                           placeholder="BRT *([\d,\.]+)"/>
                                </div>
                            </group>
                            <group id="counterpart_entry_right_column" class="col-6">
                                <field name="next_activity_type_id" placeholder="Nothing to do"/>
                            </group>
                        </group>
                        <notebook>
                            <page id="counterpart_items_tab"
                                  string="Counterpart Items"
                                  name="counterpart_items">
                                <group class="oe_inline">
                                    <field name="line_ids"
                                           default="{'default_model_id': self, 'default_company_id': self.company_id}"
                                           nolabel="1">
                                        <list editable="bottom">
                                            <field name="company_id" column_invisible="True"/>

                                            <field name="sequence"
                                                   widget="handle"/>
                                            <field name="partner_id"/>
                                            <field name="account_id"/>
                                            <field name="amount_type"/>
                                            <field name="amount_string"/>
                                            <field name="tax_ids"
                                                   widget="many2many_tax_tags"
                                                   optional="hide"
                                            />
                                            <field name="analytic_distribution" widget="analytic_distribution" string="Analytic"
                                                   groups="analytic.group_analytic_accounting"
                                                   options="{'account_field': 'account_id', 'business_domain': 'general'}"/>
                                            <field name="label"/>
                                        </list>
                                    </field>
                                </group>
                            </page>
                        </notebook>

                    </sheet>

                    <chatter/>
                </form>
            </field>
        </record>

        <record id="view_account_reconcile_model_search" model="ir.ui.view">
            <field name="name">account.reconcile.model.search</field>
            <field name="model">account.reconcile.model</field>
            <field name="arch" type="xml">
                <search string="Bank Reconciliation Move preset">
                    <field name="name"/>
                    <separator />
                    <filter string="Automated" name="auto_validate" domain="[('trigger', '=', 'auto_reconcile')]"/>
                    <separator />
                    <filter string="With tax" name="withtax" domain="[('line_ids.tax_ids', '!=', False)]"/>
                    <separator/>
                    <filter name="inactive" string="Archived" domain="[('active', '=', False)]"/>
                    <group string="Group By">
                        <filter string="Journals Availability" name="group_by_journal" context="{'group_by': 'match_journal_ids'}"/>
                        <filter string="Automation" name="group_by_auto_validate" context="{'group_by': 'trigger'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_account_reconcile_model" model="ir.actions.act_window">
            <field name="name">Reconciliation Models</field>
            <field name="res_model">account.reconcile.model</field>
            <field name="path">reconciliation-models</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="view_account_reconcile_model_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new reconciliation model
              </p><p>
                Those can be used to quickly create a journal items when reconciling
                a bank statement or an account.
              </p>
            </field>
        </record>

    </data>
</odoo>
