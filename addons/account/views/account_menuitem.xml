<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Top menu item -->
    <menuitem name="Invoicing"
        id="menu_finance"
        groups="account.group_account_readonly,account.group_account_invoice"
        web_icon="account,static/description/icon.png"
        sequence="55">
        <menuitem id="menu_board_journal_1" name="Dashboard" action="open_account_journal_dashboard_kanban" groups="account.group_account_basic" sequence="1"/>
        <menuitem id="menu_finance_receivables" name="Customers" sequence="2">
            <menuitem id="menu_action_move_out_invoice_type" action="action_move_out_invoice" sequence="1"/>
            <menuitem id="menu_action_move_out_refund_type" action="action_move_out_refund_type_non_legacy" sequence="2"/>
            <menuitem id="menu_action_account_payments_receivable" name="Payments" action="action_account_payments" sequence="15"/>
            <menuitem id="product_product_menu_sellable" name="Products" action="product_product_action_sellable" sequence="100"/>
            <menuitem id="menu_account_customer" name="Customers" action="res_partner_action_customer" sequence="110"/>
        </menuitem>
        <menuitem id="menu_finance_payables" name="Vendors" sequence="3">
            <menuitem id="menu_action_move_in_invoice_type" action="action_move_in_invoice" sequence="1"/>
            <menuitem id="menu_action_move_in_refund_type" action="action_move_in_refund_type" sequence="2"/>
            <menuitem id="menu_action_account_payments_payable" name="Payments" action="action_account_payments_payable" sequence="20"/>
            <menuitem id="product_product_menu_purchasable" name="Products" action="product_product_action_purchasable" sequence="100"/>
            <menuitem id="menu_account_supplier" name="Vendors" action="account.res_partner_action_supplier" sequence="200"/>
        </menuitem>
        <menuitem id="menu_finance_entries" name="Accounting" sequence="4" groups="account.group_account_readonly">
            <menuitem id="account_transactions_menu" name="Transactions" groups="account.group_account_readonly" sequence="0">
                <menuitem id="menu_action_move_journal_line_form" action="action_move_journal_line" groups="account.group_account_readonly" sequence="1"/>
                <menuitem id="menu_action_account_moves_all" action="action_account_moves_all" groups="account.group_account_readonly" sequence="10"/>
                <menuitem id="menu_action_analytic_lines_tree" name="Analytic Items" action="analytic.account_analytic_line_action_entries" groups="analytic.group_analytic_accounting" sequence="31"/>
            </menuitem>
            <menuitem id="account_closing_menu" name="Closing" groups="account.group_account_readonly" sequence="20"/>
        </menuitem>
        <menuitem id="menu_finance_reports" name="Reporting" sequence="20" groups="account.group_account_readonly,account.group_account_invoice">
            <menuitem id="account_reports_partners_reports_menu" name="Partner Reports" sequence="3"/>
            <menuitem id="account_reports_management_menu" name="Management" sequence="4">
                <menuitem id="menu_action_account_invoice_report_all" name="Invoice Analysis" action="action_account_invoice_report_all" sequence="1"/>
                <menuitem id="menu_action_analytic_reporting" name="Analytic Report" action="action_analytic_reporting" groups="account.group_account_readonly"/>
            </menuitem>
            <menuitem id="account_reports_legal_statements_menu" name="Statement Reports" sequence="1" groups="account.group_account_readonly"/>
        </menuitem>
        <menuitem id="menu_finance_configuration" name="Configuration" sequence="35" groups="account.group_account_manager">
            <menuitem id="menu_account_config" name="Settings" action="action_account_config" groups="base.group_system" sequence="0"/>
            <menuitem id="account_account_menu" name="Accounting" groups="account.group_account_manager" sequence="1">
                <menuitem id="menu_action_account_form" action="action_account_form" groups="account.group_account_readonly" sequence="1"/>
                <menuitem id="menu_action_tax_form" action="action_tax_form" sequence="2"/>
                <menuitem id="menu_action_account_journal_form" action="action_account_journal_form" groups="account.group_account_manager" sequence="3"/>
                <menuitem id="account_report_folder" name="Reporting" groups="account.group_account_readonly" sequence="4"/>
                <menuitem id="menu_action_currency_form" action="base.action_currency_form" name="Currencies" sequence="5"/>
                <menuitem id="menu_action_account_fiscal_position_form" action="action_account_fiscal_position_form" sequence="6"/>
                <menuitem id="menu_action_account_journal_group_list" name="Multi-Ledger" action="action_account_journal_group_list" groups="account.group_account_readonly" sequence="7"/>
                <menuitem id="menu_action_tax_group" action="action_tax_group" groups="base.group_no_one" sequence="8"/>
                <menuitem id="menu_action_rounding_form_view" action="rounding_list_action" groups="account.group_cash_rounding" sequence="9"/>
            </menuitem>
            <menuitem id="account_invoicing_menu" name="Invoicing" groups="account.group_account_invoice,account.group_account_readonly" sequence="2">
                <menuitem id="menu_action_payment_term_form" action="action_payment_term_form" sequence="10"/>
                <menuitem id="menu_action_incoterm_open" action="action_incoterms_tree" groups="base.group_no_one" sequence="20"/>
                <menuitem id="menu_product_product_categories" name="Product Categories" action="product.product_category_action_form" sequence="30"/>
            </menuitem>
            <menuitem id="root_payment_menu" name="Online Payments" groups="account.group_account_manager" sequence="3"/>
            <menuitem id="menu_analytic_accounting" name="Analytic Accounting" groups="analytic.group_analytic_accounting" sequence="4">
                <menuitem id="menu_analytic__distribution_model" name="Analytic Distribution Models" action="analytic.action_analytic_distribution_model" groups="analytic.group_analytic_accounting" sequence="10"/>
                <menuitem id="account_analytic_def_account" action="analytic.action_account_analytic_account_form" groups="analytic.group_analytic_accounting" sequence="20"/>
                <menuitem id="account_analytic_plan_menu" name="Analytic Plans" action="analytic.account_analytic_plan_action" groups="analytic.group_analytic_accounting" sequence="30"/>
            </menuitem>
        </menuitem>
    </menuitem>
</odoo>
