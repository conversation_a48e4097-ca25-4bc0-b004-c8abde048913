2025-07-02 12:05:01,647 62671 INFO ? odoo: Odoo version 18.5a1 
2025-07-02 12:05:01,648 62671 INFO ? odoo: Using configuration file at /Volumes/Trabalho/<PERSON>/odoo18/odoo.conf 
2025-07-02 12:05:01,648 62671 INFO ? odoo: addons paths: _NamespacePath(['/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons', '/Volumes/Trabalho/<PERSON>/odoo18/filestore/addons/18.5', '/Volumes/Trabalho/<PERSON>/odoo18/addons']) 
2025-07-02 12:05:01,648 62671 INFO ? odoo: database: odoo@localhost:5432 
2025-07-02 12:05:02,486 62671 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-07-02 12:05:02,494 62671 INFO ? odoo.service.server: HTTP service (werkzeug) running on iMac.local:8069 
2025-07-02 12:05:09,475 62671 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-02 12:05:09,484 62671 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-02 12:05:09,547 62671 WARNING ? odoo.modules.module_graph: module sale_async_emails: not installable, skipped 
2025-07-02 12:05:09,582 62671 INFO ? odoo.modules.module_graph: module delivery: some depends are not loaded, skipped 
2025-07-02 12:05:09,582 62671 INFO ? odoo.modules.module_graph: module website_sale_autocomplete: some depends are not loaded, skipped 
2025-07-02 12:05:09,582 62671 INFO ? odoo.modules.module_graph: module website_sale: some depends are not loaded, skipped 
2025-07-02 12:05:09,582 62671 INFO ? odoo.modules.module_graph: module l10n_br_website_sale: its direct/indirect dependency is skipped, skipped 
2025-07-02 12:05:09,582 62671 INFO ? odoo.modules.module_graph: module website: some depends are not loaded, skipped 
2025-07-02 12:05:09,582 62671 INFO ? odoo.modules.module_graph: module website_payment: its direct/indirect dependency is skipped, skipped 
2025-07-02 12:05:09,582 62671 INFO ? odoo.modules.module_graph: module website_mail: its direct/indirect dependency is skipped, skipped 
2025-07-02 12:05:09,583 62671 INFO ? odoo.modules.module_graph: module theme_default: its direct/indirect dependency is skipped, skipped 
2025-07-02 12:05:09,583 62671 INFO ? odoo.modules.module_graph: module spreadsheet_dashboard_website_sale: some depends are not loaded, skipped 
2025-07-02 12:05:09,583 62671 INFO ? odoo.modules.module_graph: module website_sms: some depends are not loaded, skipped 
2025-07-02 12:05:09,585 62671 INFO ? odoo.modules.loading: loading 69 modules... 
2025-07-02 12:05:12,120 62671 INFO ? odoo.modules.loading: 69 modules loaded in 2.53s, 0 queries (+0 extra) 
2025-07-02 12:05:12,347 62671 ERROR ? odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['delivery', 'l10n_br_website_sale', 'sale_async_emails', 'spreadsheet_dashboard_website_sale', 'theme_default', 'website', 'website_mail', 'website_payment', 'website_sale', 'website_sale_autocomplete', 'website_sms'] 
2025-07-02 12:05:12,347 62671 INFO ? odoo.modules.loading: Modules loaded. 
2025-07-02 12:05:12,372 62671 WARNING ? odoo.schema: Missing not-null constraint on server.action.history.wizard.revision 
2025-07-02 12:05:12,373 62671 WARNING ? odoo.schema: Missing not-null constraint on ir.actions.server.history.action_id 
2025-07-02 12:05:12,373 62671 WARNING ? odoo.schema: Missing not-null constraint on ir.cron.trigger.cron_id 
2025-07-02 12:05:12,373 62671 WARNING ? odoo.schema: Missing not-null constraint on ir.cron.trigger.call_at 
2025-07-02 12:05:12,373 62671 WARNING ? odoo.schema: Missing not-null constraint on res.lang.short_date_format 
2025-07-02 12:05:12,374 62671 WARNING ? odoo.schema: Missing not-null constraint on res.groups.privilege.name 
2025-07-02 12:05:12,375 62671 WARNING ? odoo.schema: Missing not-null constraint on uom.uom.relative_factor 
2025-07-02 12:05:12,375 62671 WARNING ? odoo.schema: Missing not-null constraint on auth.totp.rate.limit.log.user_id 
2025-07-02 12:05:12,375 62671 WARNING ? odoo.schema: Missing not-null constraint on mail.message.link.preview.message_id 
2025-07-02 12:05:12,375 62671 WARNING ? odoo.schema: Missing not-null constraint on mail.message.link.preview.link_preview_id 
2025-07-02 12:05:12,375 62671 WARNING ? odoo.schema: Missing not-null constraint on res.role.name 
2025-07-02 12:05:12,375 62671 WARNING ? odoo.schema: Missing not-null constraint on discuss.call.history.channel_id 
2025-07-02 12:05:12,375 62671 WARNING ? odoo.schema: Missing not-null constraint on discuss.call.history.start_dt 
2025-07-02 12:05:12,376 62671 WARNING ? odoo.schema: Missing not-null constraint on mail.activity.schedule.line.activity_schedule_id 
2025-07-02 12:05:12,376 62671 WARNING ? odoo.schema: Missing not-null constraint on mail.followers.edit.res_model 
2025-07-02 12:05:12,376 62671 WARNING ? odoo.schema: Missing not-null constraint on mail.followers.edit.operation 
2025-07-02 12:05:12,376 62671 WARNING ? odoo.schema: Missing not-null constraint on product.supplierinfo.product_uom_id 
2025-07-02 12:05:12,376 62671 WARNING ? odoo.schema: Missing not-null constraint on product.supplierinfo.product_tmpl_id 
2025-07-02 12:05:12,377 62671 WARNING ? odoo.schema: Missing not-null constraint on product.uom.uom_id 
2025-07-02 12:05:12,377 62671 WARNING ? odoo.schema: Missing not-null constraint on product.uom.product_id 
2025-07-02 12:05:12,377 62671 WARNING ? odoo.schema: Missing not-null constraint on product.uom.barcode 
2025-07-02 12:05:12,377 62671 WARNING ? odoo.schema: Missing not-null constraint on payment.method.support_manual_capture 
2025-07-02 12:05:12,377 62671 WARNING ? odoo.schema: Missing not-null constraint on account.reconcile.model.trigger 
2025-07-02 12:05:12,405 62671 INFO ? odoo.registry: Registry loaded in 3.699s 
2025-07-02 12:05:12,407 62671 INFO jose odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-02 12:05:12,433 62671 ERROR jose odoo.sql_db: bad query: b'SELECT "res_lang"."id", "res_lang"."name", "res_lang"."code", "res_lang"."iso_code", "res_lang"."url_code", "res_lang"."active", "res_lang"."direction", "res_lang"."date_format", "res_lang"."short_date_format", "res_lang"."time_format", "res_lang"."short_time_format", "res_lang"."week_start", "res_lang"."grouping", "res_lang"."decimal_point", "res_lang"."thousands_sep" FROM "res_lang" WHERE "res_lang"."active" IS TRUE ORDER BY "res_lang"."name"  '
ERROR: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".
 
2025-07-02 12:05:12,434 62671 ERROR jose odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2d76e49a0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2621, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2129, in _serve_db
    return self._transactioning(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2193, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/service/model.py", line 180, in retrying
    result = func()
             ^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2159, in _serve_ir_http
    self.registry['ir.http']._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/auth_signup/models/ir_http.py", line 13, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/web_editor/models/ir_http.py", line 24, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/http_routing/models/ir_http.py", line 481, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/web/models/ir_http.py", line 60, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/ir_http.py", line 321, in _pre_dispatch
    request.update_context(lang=get_lang(env).code)
                                ^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/misc.py", line 1296, in get_lang
    langs = [code for code, _ in env['res.lang'].get_installed()]
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 298, in get_installed
    return [(code, data.name) for code, data in self._get_active_by('code').items()]
                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 310, in _get_active_by
    langs = self.sudo().with_context(active_test=True).search_fetch([], self.CACHED_FIELDS, order='name')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 1410, in search_fetch
    return self._fetch_query(query, fields_to_fetch)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 3579, in _fetch_query
    rows = self.env.execute_query(query.select(*sql_terms))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/environments.py", line 506, in execute_query
    self.cr.execute(query)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/sql_db.py", line 422, in execute
    self._obj.execute(query, params)
psycopg2.errors.UndefinedColumn: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".

2025-07-02 12:05:12,444 62671 INFO jose werkzeug: 127.0.0.1 - - [02/Jul/2025 12:05:12] "GET / HTTP/1.1" 500 - 32 0.549 3.232
2025-07-02 12:05:12,577 62671 ERROR jose odoo.sql_db: bad query: b'SELECT "res_lang"."id", "res_lang"."name", "res_lang"."code", "res_lang"."iso_code", "res_lang"."url_code", "res_lang"."active", "res_lang"."direction", "res_lang"."date_format", "res_lang"."short_date_format", "res_lang"."time_format", "res_lang"."short_time_format", "res_lang"."week_start", "res_lang"."grouping", "res_lang"."decimal_point", "res_lang"."thousands_sep" FROM "res_lang" WHERE "res_lang"."active" IS TRUE ORDER BY "res_lang"."name"  '
ERROR: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".
 
2025-07-02 12:05:12,577 62671 ERROR jose odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2d76e49a0>, 'url_code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2d76e49a0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2621, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2110, in _serve_db
    rule, args = self.registry['ir.http']._match(self.httprequest.path)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/http_routing/models/ir_http.py", line 401, in _match
    nearest_url_lang = request.env['ir.http'].get_nearest_lang(request.env['res.lang']._get_data(url_code=url_lang_str).code or url_lang_str)
                                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 284, in _get_data
    return self._get_active_by(field_name)[field_value]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 315, in _get_active_by
    return LangDataDict({data[field]: data for data in self._get_active_by('code').values()})
                                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 310, in _get_active_by
    langs = self.sudo().with_context(active_test=True).search_fetch([], self.CACHED_FIELDS, order='name')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 1410, in search_fetch
    return self._fetch_query(query, fields_to_fetch)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 3579, in _fetch_query
    rows = self.env.execute_query(query.select(*sql_terms))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/environments.py", line 506, in execute_query
    self.cr.execute(query)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/sql_db.py", line 422, in execute
    self._obj.execute(query, params)
psycopg2.errors.UndefinedColumn: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".

2025-07-02 12:05:12,580 62671 INFO jose werkzeug: 127.0.0.1 - - [02/Jul/2025 12:05:12] "GET /favicon.ico HTTP/1.1" 500 - 4 0.045 0.022
2025-07-02 12:05:15,389 62671 ERROR jose odoo.sql_db: bad query: b'SELECT "res_lang"."id", "res_lang"."name", "res_lang"."code", "res_lang"."iso_code", "res_lang"."url_code", "res_lang"."active", "res_lang"."direction", "res_lang"."date_format", "res_lang"."short_date_format", "res_lang"."time_format", "res_lang"."short_time_format", "res_lang"."week_start", "res_lang"."grouping", "res_lang"."decimal_point", "res_lang"."thousands_sep" FROM "res_lang" WHERE "res_lang"."active" IS TRUE ORDER BY "res_lang"."name"  '
ERROR: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".
 
2025-07-02 12:05:15,391 62671 ERROR jose odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2d76e49a0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2621, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2129, in _serve_db
    return self._transactioning(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2193, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/service/model.py", line 180, in retrying
    result = func()
             ^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2159, in _serve_ir_http
    self.registry['ir.http']._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/auth_signup/models/ir_http.py", line 13, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/web_editor/models/ir_http.py", line 24, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/http_routing/models/ir_http.py", line 481, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/web/models/ir_http.py", line 60, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/ir_http.py", line 321, in _pre_dispatch
    request.update_context(lang=get_lang(env).code)
                                ^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/misc.py", line 1296, in get_lang
    langs = [code for code, _ in env['res.lang'].get_installed()]
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 298, in get_installed
    return [(code, data.name) for code, data in self._get_active_by('code').items()]
                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 310, in _get_active_by
    langs = self.sudo().with_context(active_test=True).search_fetch([], self.CACHED_FIELDS, order='name')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 1410, in search_fetch
    return self._fetch_query(query, fields_to_fetch)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 3579, in _fetch_query
    rows = self.env.execute_query(query.select(*sql_terms))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/environments.py", line 506, in execute_query
    self.cr.execute(query)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/sql_db.py", line 422, in execute
    self._obj.execute(query, params)
psycopg2.errors.UndefinedColumn: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".

2025-07-02 12:05:15,393 62671 INFO jose werkzeug: 127.0.0.1 - - [02/Jul/2025 12:05:15] "GET / HTTP/1.1" 500 - 2 0.004 0.014
2025-07-02 12:05:15,449 62671 ERROR jose odoo.sql_db: bad query: b'SELECT "res_lang"."id", "res_lang"."name", "res_lang"."code", "res_lang"."iso_code", "res_lang"."url_code", "res_lang"."active", "res_lang"."direction", "res_lang"."date_format", "res_lang"."short_date_format", "res_lang"."time_format", "res_lang"."short_time_format", "res_lang"."week_start", "res_lang"."grouping", "res_lang"."decimal_point", "res_lang"."thousands_sep" FROM "res_lang" WHERE "res_lang"."active" IS TRUE ORDER BY "res_lang"."name"  '
ERROR: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".
 
2025-07-02 12:05:15,449 62671 ERROR jose odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2d76e49a0>, 'url_code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2d76e49a0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2621, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2110, in _serve_db
    rule, args = self.registry['ir.http']._match(self.httprequest.path)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/http_routing/models/ir_http.py", line 401, in _match
    nearest_url_lang = request.env['ir.http'].get_nearest_lang(request.env['res.lang']._get_data(url_code=url_lang_str).code or url_lang_str)
                                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 284, in _get_data
    return self._get_active_by(field_name)[field_value]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 315, in _get_active_by
    return LangDataDict({data[field]: data for data in self._get_active_by('code').values()})
                                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 310, in _get_active_by
    langs = self.sudo().with_context(active_test=True).search_fetch([], self.CACHED_FIELDS, order='name')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 1410, in search_fetch
    return self._fetch_query(query, fields_to_fetch)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 3579, in _fetch_query
    rows = self.env.execute_query(query.select(*sql_terms))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/environments.py", line 506, in execute_query
    self.cr.execute(query)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/sql_db.py", line 422, in execute
    self._obj.execute(query, params)
psycopg2.errors.UndefinedColumn: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".

2025-07-02 12:05:15,451 62671 INFO jose werkzeug: 127.0.0.1 - - [02/Jul/2025 12:05:15] "GET /favicon.ico HTTP/1.1" 500 - 3 0.004 0.011
2025-07-02 12:05:27,796 62671 INFO ? odoo.service.server: Initiating shutdown 
2025-07-02 12:05:27,796 62671 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-07-02 12:05:27,892 62671 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 2 connections  
2025-07-02 12:06:27,406 63193 INFO ? odoo: Odoo version 18.5a1 
2025-07-02 12:06:27,406 63193 INFO ? odoo: Using configuration file at /Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo.conf 
2025-07-02 12:06:27,406 63193 INFO ? odoo: addons paths: _NamespacePath(['/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons', '/Volumes/Trabalho/Leonardo Ribeiro/odoo18/filestore/addons/18.5', '/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons']) 
2025-07-02 12:06:27,406 63193 INFO ? odoo: database: odoo@localhost:5432 
2025-07-02 12:06:27,711 63193 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-07-02 12:06:27,715 63193 INFO ? odoo.service.server: HTTP service (werkzeug) running on iMac.local:8069 
2025-07-02 12:06:30,723 63193 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-02 12:06:30,730 63193 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-02 12:06:30,761 63193 WARNING ? odoo.modules.module_graph: module sale_async_emails: not installable, skipped 
2025-07-02 12:06:30,793 63193 INFO ? odoo.modules.module_graph: module delivery: some depends are not loaded, skipped 
2025-07-02 12:06:30,793 63193 INFO ? odoo.modules.module_graph: module website_sale_autocomplete: some depends are not loaded, skipped 
2025-07-02 12:06:30,793 63193 INFO ? odoo.modules.module_graph: module website_sale: some depends are not loaded, skipped 
2025-07-02 12:06:30,793 63193 INFO ? odoo.modules.module_graph: module l10n_br_website_sale: its direct/indirect dependency is skipped, skipped 
2025-07-02 12:06:30,794 63193 INFO ? odoo.modules.module_graph: module website: some depends are not loaded, skipped 
2025-07-02 12:06:30,794 63193 INFO ? odoo.modules.module_graph: module website_payment: its direct/indirect dependency is skipped, skipped 
2025-07-02 12:06:30,794 63193 INFO ? odoo.modules.module_graph: module website_mail: its direct/indirect dependency is skipped, skipped 
2025-07-02 12:06:30,794 63193 INFO ? odoo.modules.module_graph: module theme_default: its direct/indirect dependency is skipped, skipped 
2025-07-02 12:06:30,794 63193 INFO ? odoo.modules.module_graph: module spreadsheet_dashboard_website_sale: some depends are not loaded, skipped 
2025-07-02 12:06:30,794 63193 INFO ? odoo.modules.module_graph: module website_sms: some depends are not loaded, skipped 
2025-07-02 12:06:30,795 63193 INFO ? odoo.modules.loading: loading 69 modules... 
2025-07-02 12:06:31,514 63193 INFO ? odoo.modules.loading: 69 modules loaded in 0.72s, 0 queries (+0 extra) 
2025-07-02 12:06:31,672 63193 ERROR ? odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['delivery', 'l10n_br_website_sale', 'sale_async_emails', 'spreadsheet_dashboard_website_sale', 'theme_default', 'website', 'website_mail', 'website_payment', 'website_sale', 'website_sale_autocomplete', 'website_sms'] 
2025-07-02 12:06:31,672 63193 INFO ? odoo.modules.loading: Modules loaded. 
2025-07-02 12:06:31,680 63193 WARNING ? odoo.schema: Missing not-null constraint on server.action.history.wizard.revision 
2025-07-02 12:06:31,680 63193 WARNING ? odoo.schema: Missing not-null constraint on ir.actions.server.history.action_id 
2025-07-02 12:06:31,680 63193 WARNING ? odoo.schema: Missing not-null constraint on ir.cron.trigger.cron_id 
2025-07-02 12:06:31,680 63193 WARNING ? odoo.schema: Missing not-null constraint on ir.cron.trigger.call_at 
2025-07-02 12:06:31,680 63193 WARNING ? odoo.schema: Missing not-null constraint on res.lang.short_date_format 
2025-07-02 12:06:31,681 63193 WARNING ? odoo.schema: Missing not-null constraint on res.groups.privilege.name 
2025-07-02 12:06:31,681 63193 WARNING ? odoo.schema: Missing not-null constraint on uom.uom.relative_factor 
2025-07-02 12:06:31,681 63193 WARNING ? odoo.schema: Missing not-null constraint on auth.totp.rate.limit.log.user_id 
2025-07-02 12:06:31,681 63193 WARNING ? odoo.schema: Missing not-null constraint on mail.message.link.preview.message_id 
2025-07-02 12:06:31,681 63193 WARNING ? odoo.schema: Missing not-null constraint on mail.message.link.preview.link_preview_id 
2025-07-02 12:06:31,682 63193 WARNING ? odoo.schema: Missing not-null constraint on res.role.name 
2025-07-02 12:06:31,682 63193 WARNING ? odoo.schema: Missing not-null constraint on discuss.call.history.channel_id 
2025-07-02 12:06:31,682 63193 WARNING ? odoo.schema: Missing not-null constraint on discuss.call.history.start_dt 
2025-07-02 12:06:31,682 63193 WARNING ? odoo.schema: Missing not-null constraint on mail.activity.schedule.line.activity_schedule_id 
2025-07-02 12:06:31,682 63193 WARNING ? odoo.schema: Missing not-null constraint on mail.followers.edit.res_model 
2025-07-02 12:06:31,682 63193 WARNING ? odoo.schema: Missing not-null constraint on mail.followers.edit.operation 
2025-07-02 12:06:31,682 63193 WARNING ? odoo.schema: Missing not-null constraint on product.supplierinfo.product_uom_id 
2025-07-02 12:06:31,682 63193 WARNING ? odoo.schema: Missing not-null constraint on product.supplierinfo.product_tmpl_id 
2025-07-02 12:06:31,682 63193 WARNING ? odoo.schema: Missing not-null constraint on product.uom.uom_id 
2025-07-02 12:06:31,683 63193 WARNING ? odoo.schema: Missing not-null constraint on product.uom.product_id 
2025-07-02 12:06:31,683 63193 WARNING ? odoo.schema: Missing not-null constraint on product.uom.barcode 
2025-07-02 12:06:31,684 63193 WARNING ? odoo.schema: Missing not-null constraint on payment.method.support_manual_capture 
2025-07-02 12:06:31,684 63193 WARNING ? odoo.schema: Missing not-null constraint on account.reconcile.model.trigger 
2025-07-02 12:06:31,708 63193 INFO ? odoo.registry: Registry loaded in 1.050s 
2025-07-02 12:06:31,709 63193 INFO jose odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-02 12:06:31,734 63193 ERROR jose odoo.sql_db: bad query: b'SELECT "res_lang"."id", "res_lang"."name", "res_lang"."code", "res_lang"."iso_code", "res_lang"."url_code", "res_lang"."active", "res_lang"."direction", "res_lang"."date_format", "res_lang"."short_date_format", "res_lang"."time_format", "res_lang"."short_time_format", "res_lang"."week_start", "res_lang"."grouping", "res_lang"."decimal_point", "res_lang"."thousands_sep" FROM "res_lang" WHERE "res_lang"."active" IS TRUE ORDER BY "res_lang"."name"  '
ERROR: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".
 
2025-07-02 12:06:31,735 63193 ERROR jose odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2cb83a3e0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2621, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2129, in _serve_db
    return self._transactioning(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2193, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/service/model.py", line 180, in retrying
    result = func()
             ^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2159, in _serve_ir_http
    self.registry['ir.http']._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/auth_signup/models/ir_http.py", line 13, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/web_editor/models/ir_http.py", line 24, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/http_routing/models/ir_http.py", line 481, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/web/models/ir_http.py", line 60, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/ir_http.py", line 321, in _pre_dispatch
    request.update_context(lang=get_lang(env).code)
                                ^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/misc.py", line 1296, in get_lang
    langs = [code for code, _ in env['res.lang'].get_installed()]
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 298, in get_installed
    return [(code, data.name) for code, data in self._get_active_by('code').items()]
                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 310, in _get_active_by
    langs = self.sudo().with_context(active_test=True).search_fetch([], self.CACHED_FIELDS, order='name')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 1410, in search_fetch
    return self._fetch_query(query, fields_to_fetch)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 3579, in _fetch_query
    rows = self.env.execute_query(query.select(*sql_terms))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/environments.py", line 506, in execute_query
    self.cr.execute(query)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/sql_db.py", line 422, in execute
    self._obj.execute(query, params)
psycopg2.errors.UndefinedColumn: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".

2025-07-02 12:06:31,745 63193 INFO jose werkzeug: 127.0.0.1 - - [02/Jul/2025 12:06:31] "GET / HTTP/1.1" 500 - 20 0.075 1.072
2025-07-02 12:06:32,481 63193 ERROR jose odoo.sql_db: bad query: b'SELECT "res_lang"."id", "res_lang"."name", "res_lang"."code", "res_lang"."iso_code", "res_lang"."url_code", "res_lang"."active", "res_lang"."direction", "res_lang"."date_format", "res_lang"."short_date_format", "res_lang"."time_format", "res_lang"."short_time_format", "res_lang"."week_start", "res_lang"."grouping", "res_lang"."decimal_point", "res_lang"."thousands_sep" FROM "res_lang" WHERE "res_lang"."active" IS TRUE ORDER BY "res_lang"."name"  '
ERROR: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".
 
2025-07-02 12:06:32,481 63193 ERROR jose odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2cb83a3e0>, 'url_code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2cb83a3e0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2621, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2110, in _serve_db
    rule, args = self.registry['ir.http']._match(self.httprequest.path)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/http_routing/models/ir_http.py", line 401, in _match
    nearest_url_lang = request.env['ir.http'].get_nearest_lang(request.env['res.lang']._get_data(url_code=url_lang_str).code or url_lang_str)
                                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 284, in _get_data
    return self._get_active_by(field_name)[field_value]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 315, in _get_active_by
    return LangDataDict({data[field]: data for data in self._get_active_by('code').values()})
                                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 310, in _get_active_by
    langs = self.sudo().with_context(active_test=True).search_fetch([], self.CACHED_FIELDS, order='name')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 1410, in search_fetch
    return self._fetch_query(query, fields_to_fetch)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 3579, in _fetch_query
    rows = self.env.execute_query(query.select(*sql_terms))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/environments.py", line 506, in execute_query
    self.cr.execute(query)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/sql_db.py", line 422, in execute
    self._obj.execute(query, params)
psycopg2.errors.UndefinedColumn: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".

2025-07-02 12:06:32,483 63193 INFO jose werkzeug: 127.0.0.1 - - [02/Jul/2025 12:06:32] "GET /favicon.ico HTTP/1.1" 500 - 4 0.006 0.011
2025-07-02 12:06:51,210 63193 ERROR jose odoo.sql_db: bad query: b'SELECT "res_lang"."id", "res_lang"."name", "res_lang"."code", "res_lang"."iso_code", "res_lang"."url_code", "res_lang"."active", "res_lang"."direction", "res_lang"."date_format", "res_lang"."short_date_format", "res_lang"."time_format", "res_lang"."short_time_format", "res_lang"."week_start", "res_lang"."grouping", "res_lang"."decimal_point", "res_lang"."thousands_sep" FROM "res_lang" WHERE "res_lang"."active" IS TRUE ORDER BY "res_lang"."name"  '
ERROR: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".
 
2025-07-02 12:06:51,211 63193 ERROR jose odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2cb83a3e0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2621, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2129, in _serve_db
    return self._transactioning(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2193, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/service/model.py", line 180, in retrying
    result = func()
             ^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2159, in _serve_ir_http
    self.registry['ir.http']._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/auth_signup/models/ir_http.py", line 13, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/web_editor/models/ir_http.py", line 24, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/http_routing/models/ir_http.py", line 481, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/web/models/ir_http.py", line 60, in _pre_dispatch
    super()._pre_dispatch(rule, args)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/ir_http.py", line 321, in _pre_dispatch
    request.update_context(lang=get_lang(env).code)
                                ^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/misc.py", line 1296, in get_lang
    langs = [code for code, _ in env['res.lang'].get_installed()]
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 298, in get_installed
    return [(code, data.name) for code, data in self._get_active_by('code').items()]
                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 310, in _get_active_by
    langs = self.sudo().with_context(active_test=True).search_fetch([], self.CACHED_FIELDS, order='name')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 1410, in search_fetch
    return self._fetch_query(query, fields_to_fetch)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 3579, in _fetch_query
    rows = self.env.execute_query(query.select(*sql_terms))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/environments.py", line 506, in execute_query
    self.cr.execute(query)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/sql_db.py", line 422, in execute
    self._obj.execute(query, params)
psycopg2.errors.UndefinedColumn: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".

2025-07-02 12:06:51,212 63193 INFO jose werkzeug: 127.0.0.1 - - [02/Jul/2025 12:06:51] "GET / HTTP/1.1" 500 - 2 0.004 0.011
2025-07-02 12:06:51,940 63193 ERROR jose odoo.sql_db: bad query: b'SELECT "res_lang"."id", "res_lang"."name", "res_lang"."code", "res_lang"."iso_code", "res_lang"."url_code", "res_lang"."active", "res_lang"."direction", "res_lang"."date_format", "res_lang"."short_date_format", "res_lang"."time_format", "res_lang"."short_time_format", "res_lang"."week_start", "res_lang"."grouping", "res_lang"."decimal_point", "res_lang"."thousands_sep" FROM "res_lang" WHERE "res_lang"."active" IS TRUE ORDER BY "res_lang"."name"  '
ERROR: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".
 
2025-07-02 12:06:51,940 63193 ERROR jose odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2cb83a3e0>, 'url_code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 124, in lookup
    r = d[key]
        ~^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/lru.py", line 57, in __getitem__
    val = self._values[key]
          ~~~~~~~~~~~~^^^^^
KeyError: ('res.lang', <function ResLang._get_active_by at 0x2cb83a3e0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2621, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/http.py", line 2110, in _serve_db
    rule, args = self.registry['ir.http']._match(self.httprequest.path)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/addons/http_routing/models/ir_http.py", line 401, in _match
    nearest_url_lang = request.env['ir.http'].get_nearest_lang(request.env['res.lang']._get_data(url_code=url_lang_str).code or url_lang_str)
                                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 284, in _get_data
    return self._get_active_by(field_name)[field_value]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 315, in _get_active_by
    return LangDataDict({data[field]: data for data in self._get_active_by('code').values()})
                                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/venv/lib/python3.12/site-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/tools/cache.py", line 131, in lookup
    value = self.method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/addons/base/models/res_lang.py", line 310, in _get_active_by
    langs = self.sudo().with_context(active_test=True).search_fetch([], self.CACHED_FIELDS, order='name')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 1410, in search_fetch
    return self._fetch_query(query, fields_to_fetch)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/models.py", line 3579, in _fetch_query
    rows = self.env.execute_query(query.select(*sql_terms))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/orm/environments.py", line 506, in execute_query
    self.cr.execute(query)
  File "/Volumes/Trabalho/Leonardo Ribeiro/odoo18/odoo/sql_db.py", line 422, in execute
    self._obj.execute(query, params)
psycopg2.errors.UndefinedColumn: column res_lang.short_date_format does not exist
LINE 1: ..."res_lang"."direction", "res_lang"."date_format", "res_lang"...
                                                             ^
HINT:  Perhaps you meant to reference the column "res_lang.short_time_format".

2025-07-02 12:06:51,942 63193 INFO jose werkzeug: 127.0.0.1 - - [02/Jul/2025 12:06:51] "GET /favicon.ico HTTP/1.1" 500 - 3 0.003 0.011
2025-07-02 12:06:59,224 63193 INFO ? odoo.service.server: Initiating server reload 
2025-07-02 12:06:59,436 63193 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 2 connections  
